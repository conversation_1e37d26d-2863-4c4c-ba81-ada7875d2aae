name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.12.8]

    services:
      postgres:
        image: pgvector/pgvector:pg16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          # Maps tcp port 5432 on service container to the host
          - 5432:5432

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install poetry
        run: |
          python -m pip install poetry==2.1.1

      - name: Configure poetry
        run: |
          python -m poetry config virtualenvs.in-project true

      - name: Cache the virtualenv
        id: cache
        uses: actions/cache@v3
        with:
          path: ./.venv
          key: ${{ runner.os }}-venv-${{ hashFiles('**/poetry.lock') }}

      - name: Install Dependencies
        run: poetry install
        if: steps.cache.outputs.cache-hit != 'true'

      - name: Run Mypy
        run: |
          poetry run mypy .

      - name: Run Tests
        run: poetry run pytest
        env:
          TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          TEST_ASYNC_DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/test_db
          OPENAI_API_KEY: "dummy_key"
          SECRET_KEY: "b1f554abf087812b112426b114a36486288e660867fb6f94842af52bec69021e"
          GEMINI_API_KEY: "dummy_key"
          LINKUP_API_KEY: "dummy_key"
