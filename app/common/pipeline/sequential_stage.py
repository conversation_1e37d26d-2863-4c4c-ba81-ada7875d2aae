from app.common.pipeline.base_stage import BaseStage


class SequentialStage(BaseStage):
    """
    A composite stage that executes multiple stages in sequence.
    """

    def __init__(
        self,
        stages: list[BaseStage],
        interval_seconds: int = 60,
        stage_id: str | None = None,
    ):
        """
        Initialize the sequential stage.

        Args:
            stages: List of stages to execute in sequence.
            interval_seconds: Interval in seconds between executions.
                              Default is 60 seconds.
            stage_id: Unique identifier for the stage.
                      If not provided, a UUID will be generated.
        """
        super().__init__(stage_id=stage_id, interval_seconds=interval_seconds)
        self.stages = stages

    async def execute_once(self):
        """Execute all stages in sequence once."""
        results = {}

        for i, stage in enumerate(self.stages):
            if self.should_stop:
                self.logger.info("Sequential execution stopped early")
                break

            stage_name = stage.__class__.__name__
            self.logger.info(
                f"Executing stage {i + 1}/{len(self.stages)}: {stage_name}"
            )

            try:
                results[stage.stage_id] = await stage.execute_once()
            except Exception as e:
                self.logger.exception("Error executing %s", stage_name)
                results[stage.stage_id] = {"error": str(e)}

        return results

    def stop(self):
        """Stop this stage and all child stages."""
        super().stop()
        # Also stop all child stages in case they are running independently
        for stage in self.stages:
            stage.stop()

    def get_status(self) -> dict:
        """Get status including child stages."""
        status = super().get_status()
        status.update(
            {
                "stages_count": len(self.stages),
                "interval_seconds": self.interval_seconds,
                "child_stages": [
                    {
                        "id": stage.stage_id,
                        "name": stage.__class__.__name__,
                        "should_stop": stage.should_stop,
                    }
                    for stage in self.stages
                ],
            }
        )
        return status
