import pytest

from app.common.pipeline.base_stage import BaseStage


class MockStage(BaseStage):
    def __init__(self, execute_count=1, stage_id=None):
        super().__init__(stage_id)
        self.execute_count = execute_count
        self.current_count = 0
        self.executed_results = []

    async def execute_once(self):
        self.current_count += 1
        result = f"Execution {self.current_count}"
        self.executed_results.append(result)
        return result


@pytest.mark.anyio
async def test_base_stage_execute_once():
    stage = MockStage()
    result = await stage.execute_once()
    assert result == "Execution 1"


def test_base_stage_stop():
    stage = MockStage()
    stage.stop()
    assert stage.should_stop is True


def test_base_stage_get_status():
    stage = MockStage(stage_id="test-id")
    status = stage.get_status()
    assert status == {"id": "test-id", "name": "MockStage", "should_stop": False}


def test_base_stage_unique_id():
    stage1 = MockStage()
    stage2 = MockStage()
    assert stage1.stage_id != stage2.stage_id


@pytest.mark.anyio
async def test_base_stage_execute_with_stop(mocker):
    stage = MockStage()

    mocker.patch("asyncio.sleep", return_value=None)

    async def mock_execute_once():
        stage.should_stop = True
        return "Stopping"

    stage.execute_once = mock_execute_once

    await stage.execute()
    assert stage.should_stop is True
