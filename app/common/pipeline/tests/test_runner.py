import asyncio

import pytest

from app.common.pipeline.base_stage import BaseStage
from app.common.pipeline.runner import PipelineRunner


class MockStage(BaseStage):
    def __init__(self, name):
        super().__init__()
        self.name = name
        self.executed = False

    async def execute_once(self):
        self.executed = True
        await asyncio.sleep(0.01)
        return f"Result from {self.name}"

    async def execute(self):
        await self.execute_once()
        while not self.should_stop:
            await asyncio.sleep(0.1)


def test_pipeline_runner_add_stage():
    runner = PipelineRunner()
    stage = MockStage("Test Stage")
    runner.add_stage(stage)

    assert len(runner.stages) == 1
    assert runner.stages[0] == stage


@pytest.mark.anyio
async def test_pipeline_runner_run_single_stage():
    stage = MockStage("Test Stage")
    runner = PipelineRunner([stage])

    result = await runner.run()
    assert result == "Result from Test Stage"
    assert stage.executed


@pytest.mark.anyio
async def test_pipeline_runner_run_multiple_stages():
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = <PERSON>pelineRunner([stage1, stage2])

    result = await runner.run()

    assert isinstance(result, dict)
    assert len(result) == 2  # Two stages executed
    assert stage1.executed
    assert stage2.executed


@pytest.mark.anyio
async def test_pipeline_runner_start_daemon_parallel():
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = PipelineRunner([stage1, stage2])

    await runner.start_daemon()

    assert runner.is_running
    assert not stage1.should_stop
    assert not stage2.should_stop
    assert len(runner._tasks) == 2

    await asyncio.sleep(0.1)

    await runner.stop_daemon()

    assert not runner.is_running
    assert stage1.should_stop
    assert stage2.should_stop
    assert runner._tasks == {}


@pytest.mark.anyio
async def test_pipeline_runner_start_daemon_sequential():
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = PipelineRunner([stage1, stage2])

    await runner.start_daemon(sequential_execution=True, sequential_interval_seconds=1)

    assert runner.is_running
    assert len(runner._tasks) == 1  # One sequential task

    await asyncio.sleep(0.1)

    await runner.stop_daemon()

    assert not runner.is_running


@pytest.mark.anyio
async def test_pipeline_runner_start_daemon_blocking():
    stage1 = MockStage("Stage1")
    runner = PipelineRunner([stage1])

    async def stop_after_delay():
        await asyncio.sleep(0.2)
        await runner.stop_daemon()

    stop_task = asyncio.create_task(stop_after_delay())

    await runner.start_daemon(wait=True)

    assert not runner.is_running
    assert stage1.should_stop

    await stop_task


@pytest.mark.anyio
async def test_pipeline_runner_wait_for_completion():
    stage1 = MockStage("Stage1")
    runner = PipelineRunner([stage1])

    await runner.start_daemon()
    assert runner.is_running

    async def stop_after_delay():
        await asyncio.sleep(0.2)
        await runner.stop_daemon()

    stop_task = asyncio.create_task(stop_after_delay())

    await runner.wait_for_completion()

    assert not runner.is_running

    await stop_task


@pytest.mark.anyio
async def test_pipeline_runner_stop_daemon():
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = PipelineRunner([stage1, stage2])

    await runner.start_daemon()

    await asyncio.sleep(0.1)

    assert runner.is_running
    assert len(runner._tasks) > 0

    await runner.stop_daemon()

    assert not runner.is_running
    assert runner.should_stop
    assert runner._tasks == {}


@pytest.mark.anyio
async def test_pipeline_runner_graceful_shutdown():
    class SlowStopStage(BaseStage):
        def __init__(self):
            super().__init__()
            self.stop_called = False
            self.cleanup_done = False

        async def execute_once(self):
            return "result"

        async def execute(self):
            while not self.should_stop:
                await asyncio.sleep(0.05)
            await asyncio.sleep(0.1)
            self.cleanup_done = True

        def stop(self):
            super().stop()
            self.stop_called = True

    stage = SlowStopStage()
    runner = PipelineRunner([stage])

    await runner.start_daemon()

    await asyncio.sleep(0.1)

    await runner.stop_daemon()

    assert stage.stop_called
    assert stage.cleanup_done
    assert not runner.is_running


def test_pipeline_runner_get_status():
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = PipelineRunner([stage1, stage2])

    status = runner.get_status()

    assert status["running"] is False
    assert status["should_stop"] is False
    assert len(status["stages"]) == 2
    assert status["stages"][0]["name"] == "MockStage"
    assert status["stages"][1]["name"] == "MockStage"
    assert status["active_tasks"] == 0


@pytest.mark.anyio
async def test_pipeline_runner_no_stages():
    runner = PipelineRunner()

    result = await runner.run()
    assert result == {}

    await runner.start_daemon()
    assert not runner.is_running


@pytest.mark.anyio
async def test_pipeline_runner_exception_handling():
    class FailingStage(BaseStage):
        async def execute_once(self):
            raise ValueError("Test error")

        async def execute(self):
            raise ValueError("Test daemon error")

    stage = FailingStage()
    runner = PipelineRunner([stage])

    result = await runner.run()
    assert isinstance(result, dict)
    assert result["status"] == "error"
    assert "Test error" in result["error"]

    await runner.start_daemon()
    await asyncio.sleep(0.1)

    await runner.stop_daemon()

    assert not runner.is_running


@pytest.mark.anyio
async def test_pipeline_runner_already_running_error():
    stage = MockStage("Stage1")
    runner = PipelineRunner([stage])

    await runner.start_daemon()
    assert runner.is_running

    with pytest.raises(RuntimeError, match="already running"):
        await runner.start_daemon()

    await runner.stop_daemon()
