import pytest

from app.common.pipeline.base_stage import BaseStage
from app.common.pipeline.sequential_stage import SequentialStage


class MockStage(BaseStage):
    def __init__(self, name, stage_id=None):
        super().__init__(stage_id)
        self.name = name
        self.executed = False

    async def execute_once(self):
        self.executed = True
        return f"Result from {self.name}"


@pytest.mark.anyio
async def test_sequential_stage_execution():
    stage1 = MockStage("Stage 1")
    stage2 = MockStage("Stage 2")

    sequential_stage = SequentialStage([stage1, stage2])
    results = await sequential_stage.execute_once()

    assert len(results) == 2
    assert all(isinstance(key, str) for key in results)
    assert stage1.executed
    assert stage2.executed


def test_sequential_stage_stop():
    stage1 = MockStage("Stage 1")
    stage2 = MockStage("Stage 2")

    sequential_stage = SequentialStage([stage1, stage2])
    sequential_stage.stop()

    assert sequential_stage.should_stop
    assert stage1.should_stop
    assert stage2.should_stop


def test_sequential_stage_get_status():
    stage1 = MockStage("Stage 1")
    stage2 = MockStage("Stage 2")

    sequential_stage = SequentialStage([stage1, stage2], interval_seconds=30)
    status = sequential_stage.get_status()

    assert status["stages_count"] == 2
    assert status["interval_seconds"] == 30
    assert len(status["child_stages"]) == 2

    assert status["child_stages"][0]["id"] != status["child_stages"][1]["id"]

    assert all(stage["name"] == "MockStage" for stage in status["child_stages"])
