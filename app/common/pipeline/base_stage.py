import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod

from app.common.helpers.logger import get_logger


class BaseStage(ABC):
    """
    Abstract base class for pipeline stages.
    """

    def __init__(
        self,
        stage_id: str | None = None,
        interval_seconds: int = 60,
        logger: logging.Logger | None = None,
    ):
        """
        Initialize the stage with an optional unique identifier and interval.

        Args:
            stage_id: Unique identifier for the stage.
                      If not provided, a UUID will be generated.
            interval_seconds: Interval in seconds between executions.
                              Default is 60 seconds.
            logger: Logger instance to use.
        """
        self.stage_id = stage_id or str(uuid.uuid4())
        self.interval_seconds = interval_seconds
        self.logger = logger or get_logger(self.__class__.__name__)
        self.should_stop = False

    @abstractmethod
    async def execute_once(self) -> dict:
        """
        Execute the stage's primary functionality once.

        This method must be implemented by all stage subclasses.
        """
        pass

    async def execute(self):
        """
        Execute in daemon mode until stopped.
        """
        self.should_stop = False

        self.logger.info(
            f"Starting continuous execution with interval {self.interval_seconds}s"
        )

        while not self.should_stop:
            try:
                start_time = time.time()

                # Execute once
                await self.execute_once()

                # Calculate execution time
                execution_time = time.time() - start_time

                # Log execution result
                self.logger.info(f"Execution completed in {execution_time:.2f}s")

                # Sleep for the specified interval
                for _ in range(self.interval_seconds):
                    if self.should_stop:
                        break
                    await asyncio.sleep(1)

            except Exception:
                self.logger.exception(
                    "Error in execution: status=%s", self.get_status()
                )
                # Wait before retrying to avoid tight loops on error
                for _ in range(5):  # ~5 seconds
                    if self.should_stop:
                        break
                    await asyncio.sleep(1)

    def stop(self):
        """Signal the stage to stop execution."""
        self.should_stop = True
        self.logger.info("Stop signal received")

    def get_status(self) -> dict:
        """Get status information."""
        return {
            "id": self.stage_id,
            "name": self.__class__.__name__,
            "should_stop": self.should_stop,
        }
