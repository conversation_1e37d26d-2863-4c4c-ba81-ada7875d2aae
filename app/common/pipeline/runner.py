import asyncio
from typing import Any

from app.common.helpers.logger import get_logger
from app.common.pipeline.base_stage import BaseStage
from app.common.pipeline.sequential_stage import SequentialStage


class PipelineRunner:
    """
    Async runner for pipeline stages with daemon support and controlled stopping.
    """

    def __init__(self, stages: list[BaseStage] | None = None):
        """Initialize the pipeline runner."""
        self.stages = stages or []
        self.logger = get_logger(self.__class__.__name__)

        # Task management
        self._tasks: dict[str, asyncio.Task] = {}
        self._composite_stages: list[BaseStage] = []
        self.is_running = False

        # Daemon control
        self.should_stop = False

    def add_stage(self, stage: BaseStage) -> None:
        """
        Add a stage to the pipeline.

        Args:
            stage: The stage to add

        Raises:
            RuntimeError: If the pipeline is already running
        """
        if self.is_running:
            raise RuntimeError("Cannot add stages while the pipeline is running")

        self.stages.append(stage)
        self.logger.info(f"Added stage: {stage.__class__.__name__}")

    async def run(self) -> dict[str, Any]:
        """
        Execute all stages once.

        Returns:
            Results of the execution
        """
        if len(self.stages) == 1:
            # Single stage - execute directly with error handling
            try:
                return await self.stages[0].execute_once()
            except Exception as e:
                self.logger.exception(f"Error in single stage execution: {e}")
                return {"status": "error", "error": str(e)}
        elif len(self.stages) > 1:
            # Multiple stages - create a sequential stage
            sequential = SequentialStage(self.stages)
            return await sequential.execute_once()
        else:
            self.logger.warning("No stages to execute")
            return {}

    async def start_daemon(
        self,
        sequential_execution: bool = False,
        sequential_interval_seconds: int = 60,
        wait: bool = False,
    ) -> None:
        """
        Start the pipeline in daemon mode using asyncio tasks.

        Args:
            sequential_execution: Whether to run stages sequentially
            sequential_interval_seconds: Interval between sequential executions
            wait: If True, wait for completion. If False, return immediately.

        Raises:
            RuntimeError: If the pipeline is already running
        """
        # Check if already running
        if self.is_running:
            raise RuntimeError("Pipeline is already running")

        # Check if we have stages to run
        if not self.stages:
            self.logger.warning("No stages to execute in daemon mode")
            return

        # Reset daemon control flags
        self.should_stop = False
        self.is_running = True

        # Keep track of composite stages for proper stopping
        self._composite_stages = []

        if sequential_execution and len(self.stages) > 1:
            # Sequential mode - create a composite stage
            sequential_stage = SequentialStage(
                self.stages, interval_seconds=sequential_interval_seconds
            )
            self._composite_stages.append(sequential_stage)

            stage_name = "sequential"
            task = asyncio.create_task(
                self._run_stage_daemon(sequential_stage, stage_name), name=stage_name
            )
            self._tasks[stage_name] = task

        else:
            # Parallel mode - run each stage concurrently
            for stage in self.stages:
                stage_name = f"{stage.__class__.__name__}_{stage.stage_id}"
                task = asyncio.create_task(
                    self._run_stage_daemon(stage, stage_name), name=stage_name
                )
                self._tasks[stage_name] = task

        self.logger.info(
            f"Pipeline daemon started in {'sequential' if sequential_execution else 'parallel'} mode"
        )

        # Wait if requested
        if wait:
            await self.wait_for_completion()

    async def wait_for_completion(self) -> None:
        """
        Wait for all daemon tasks to complete.
        This is a separate method so start_daemon can return immediately.
        """
        if not self.is_running:
            self.logger.warning("Pipeline is not running")
            return

        if self._tasks:
            try:
                self.logger.info("Waiting for all tasks to complete...")
                await asyncio.gather(*self._tasks.values(), return_exceptions=True)
            except Exception as e:
                self.logger.exception(f"Error in pipeline daemon: {e}")
            finally:
                await self.stop_daemon()

    async def _run_stage_daemon(self, stage: BaseStage, name: str):
        """Run a stage in daemon mode."""
        self.logger.info(f"Started daemon for {name}")

        try:
            await stage.execute()
        except Exception as e:
            self.logger.exception(f"Error in stage {name}: {e}")
        finally:
            self.logger.info(f"Daemon for {name} stopped")

    async def stop_daemon(self) -> None:
        """Stop all daemon tasks."""
        if not self.is_running:
            self.logger.info("Pipeline is not running")
            return

        # Set stop flags
        self.should_stop = True
        self.is_running = False

        self.logger.info("Stopping pipeline...")

        # Signal all stages to stop
        for stage in self.stages:
            stage.stop()

        # Also stop composite stages (like SequentialStage)
        for composite_stage in self._composite_stages:
            composite_stage.stop()

        # Give stages time to stop gracefully
        graceful_timeout = 5  # seconds
        self.logger.info(
            f"Waiting {graceful_timeout}s for stages to stop gracefully..."
        )

        try:
            await asyncio.wait_for(
                asyncio.gather(
                    *[task for task in self._tasks.values() if not task.done()],
                    return_exceptions=True,
                ),
                timeout=graceful_timeout,
            )
            self.logger.info("All stages stopped gracefully")
        except TimeoutError:
            self.logger.info("Graceful timeout reached, forcing cancellation...")

            # Force cancel remaining tasks
            for name, task in list(self._tasks.items()):
                if not task.done():
                    self.logger.info(f"Force cancelling task {name}...")
                    task.cancel()

                    try:
                        await asyncio.wait_for(task, timeout=5)
                    except (TimeoutError, asyncio.CancelledError):
                        self.logger.info(f"Task {name} cancelled")
                    except Exception as e:
                        self.logger.warning(f"Error stopping task {name}: {e}")

        # Clean up
        self._tasks = {}
        self._composite_stages = []

        self.logger.info("Pipeline stopped")

    def get_status(self) -> dict[str, Any]:
        """Get the current pipeline status."""
        return {
            "running": self.is_running,
            "should_stop": self.should_stop,
            "stages": [
                {
                    "name": stage.__class__.__name__,
                    "stage_id": stage.stage_id,
                    "running": any(
                        stage.stage_id in task_name and not task.done()
                        for task_name, task in self._tasks.items()
                    ),
                    "should_stop": stage.should_stop,
                }
                for stage in self.stages
            ],
            "active_tasks": len([t for t in self._tasks.values() if not t.done()]),
        }
