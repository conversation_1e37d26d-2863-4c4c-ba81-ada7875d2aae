from uuid import uuid4

import pytest

from app.agentic.context.crm_tools import Async<PERSON><PERSON>roxy


@pytest.fixture
def proxy(user_id):
    environment_id = uuid4()
    return AsyncCRMProxy(user_id, environment_id)


@pytest.mark.anyio
async def test_async_crm_proxy_init(user_id):
    environment_id = uuid4()
    proxy = AsyncCRMProxy(user_id, environment_id)

    assert proxy.user_id == user_id
    assert proxy.environment_id == environment_id


@pytest.mark.anyio
async def test_execute_crm_method(mocker, proxy, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Test Opp"}
    )

    result = await proxy._execute_crm_method("get_opportunity", "001")

    assert result == {"Id": "001", "Name": "Test Opp"}

    mock_create_user_integrations.assert_called_once_with(
        user_id=proxy.user_id,
        environment_id=proxy.environment_id,
        db_session=mock_session,
    )
    mock_crm_provider.get_opportunity.assert_called_once_with("001")


@pytest.mark.anyio
async def test_execute_crm_method_no_integration(mocker, proxy):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return None

    mock_user_integrations.crm = mock_crm

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    with pytest.raises(
        RuntimeError, match=f"No CRM integration for user {proxy.user_id}"
    ):
        await proxy._execute_crm_method("get_opportunity", "001")

    mock_create_user_integrations.assert_called_once_with(
        user_id=proxy.user_id,
        environment_id=proxy.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_get_current_user_territory(mocker, proxy):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_accounts = [
        {"id": "acc_1", "name": "Territory Account 1", "territory": "North"},
        {"id": "acc_2", "name": "Territory Account 2", "territory": "South"},
    ]

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(
        return_value=mock_accounts
    )

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    result = await proxy.get_current_user_territory()

    assert result == mock_accounts

    mock_create_user_integrations.assert_called_once_with(
        user_id=proxy.user_id,
        environment_id=proxy.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_get_current_user_territory_empty(mocker, proxy):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    result = await proxy.get_current_user_territory()

    assert result == []

    mock_create_user_integrations.assert_called_once_with(
        user_id=proxy.user_id,
        environment_id=proxy.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_getattr_dynamic_methods(mocker, proxy, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Test Opp"}
    )
    mock_crm_provider.update_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Updated Opp"}
    )
    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={"Id": "002", "Name": "Test Account"}
    )
    mock_crm_provider.create_contact = mocker.AsyncMock(
        return_value={"Id": "003", "Name": "New Contact"}
    )

    result1 = await proxy.get_opportunity("001")
    assert result1 == {"Id": "001", "Name": "Test Opp"}
    mock_crm_provider.get_opportunity.assert_called_with("001")

    result2 = await proxy.update_opportunity("001", {"Name": "Updated Opp"})
    assert result2 == {"Id": "001", "Name": "Updated Opp"}
    mock_crm_provider.update_opportunity.assert_called_with(
        "001", {"Name": "Updated Opp"}
    )

    result3 = await proxy.get_account("002")
    assert result3 == {"Id": "002", "Name": "Test Account"}
    mock_crm_provider.get_account.assert_called_with("002")

    result4 = await proxy.create_contact({"Name": "New Contact"})
    assert result4 == {"Id": "003", "Name": "New Contact"}
    mock_crm_provider.create_contact.assert_called_with({"Name": "New Contact"})


@pytest.mark.anyio
async def test_getattr_with_args_and_kwargs(mocker, proxy, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.search_opportunities = mocker.AsyncMock(
        return_value=[{"Id": "001", "Name": "Found Opp"}]
    )

    criteria = {"Name": "Test"}
    limit = 10
    result = await proxy.search_opportunities(criteria, limit=limit, sort="Name")

    assert result == [{"Id": "001", "Name": "Found Opp"}]
    mock_crm_provider.search_opportunities.assert_called_with(
        criteria, limit=10, sort="Name"
    )


@pytest.mark.anyio
async def test_multiple_concurrent_calls(mocker, proxy, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Opp"}
    )
    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={"Id": "002", "Name": "Account"}
    )
    mock_crm_provider.get_contact = mocker.AsyncMock(
        return_value={"Id": "003", "Name": "Contact"}
    )

    import asyncio

    results = await asyncio.gather(
        proxy.get_opportunity("001"), proxy.get_account("002"), proxy.get_contact("003")
    )

    assert results[0] == {"Id": "001", "Name": "Opp"}
    assert results[1] == {"Id": "002", "Name": "Account"}
    assert results[2] == {"Id": "003", "Name": "Contact"}

    mock_crm_provider.get_opportunity.assert_called_with("001")
    mock_crm_provider.get_account.assert_called_with("002")
    mock_crm_provider.get_contact.assert_called_with("003")
