import uuid
from unittest.mock import AsyncMock

import pytest
from langchain_core.messages import AIMessage, AIMessageChunk, HumanMessage, ToolMessage
from langfuse.callback import CallbackHandler
from langgraph.checkpoint.postgres import CheckpointTuple
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from app.agentic.graph.graph_manager import GraphManager


@pytest.fixture
def mock_compiled_graph(mocker):
    mock = mocker.AsyncMock(spec=CompiledStateGraph)
    mock.ainvoke = AsyncMock(return_value={"messages": ["mocked output"]})
    return mock


@pytest.fixture
def mock_langfuse_callback_handler(mocker):
    return mocker.Mock(spec=CallbackHandler)


@pytest.fixture
def graph_manager(
    mock_compiled_graph,
    mock_langfuse_callback_handler,
):
    runner = GraphManager(
        graph=mock_compiled_graph,
        langfuse_callback_handler=mock_langfuse_callback_handler,
    )

    assert runner.graph is mock_compiled_graph

    return runner


@pytest.mark.anyio
async def test_stream_graph(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    async def mock_astream(*_, **__):
        yield "messages", (AIMessageChunk(content="Hello "), {})
        yield "messages", (AIMessageChunk(content="World!"), {})

    patched_astream = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream
    patched_astream.assert_called_once()

    _, call_kwargs = patched_astream.call_args

    assert "input" in call_kwargs
    assert call_kwargs["input"] == graph_input
    assert "config" in call_kwargs
    config = call_kwargs["config"]
    assert config["configurable"]["thread_id"] == thread_id
    assert config["configurable"]["checkpoint_ns"] == ""
    assert config["callbacks"] == [graph_manager.langfuse_callback_handler]

    assert patched_process.call_count == 2
    patched_process.assert_has_calls(
        [
            mocker.call(AIMessageChunk(content="Hello ")),
            mocker.call(AIMessageChunk(content="World!")),
        ]
    )


@pytest.mark.anyio
async def test_stream_graph_with_interrupt(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    interrupt = Interrupt(value="test_interrupt", resumable=True, ns="test_ns")

    async def mock_astream(*_, **__):
        yield "updates", {"__interrupt__": (interrupt, "Interrupted")}

    patched_astream_events = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream

    patched_astream_events.assert_called_once()

    patched_process.assert_called_once_with(interrupt)


@pytest.mark.anyio
async def test_stream_graph_with_command(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    async def mock_astream(*_, **__):
        yield "messages", (Command(update="Command update message"), {})

    patched_astream = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream

    patched_astream.assert_called_once()

    patched_process.assert_called_once_with(Command(update="Command update message"))


def test_parse_historical_message_data_with_ai_message(graph_manager):
    ai_message = AIMessage(id="1234", content="AI response")

    result = graph_manager._parse_historical_message_data(ai_message)

    assert result == {
        "id": "1234",
        "role": "assistant",
        "content": {"type": "text", "text": "AI response"},
    }


def test_parse_historical_message_data_with_human_message(graph_manager):
    human_message = HumanMessage(id="5678", content="Human question")

    result = graph_manager._parse_historical_message_data(human_message)
    assert result == {
        "id": "5678",
        "role": "user",
        "content": {"type": "text", "text": "Human question"},
    }


def test_get_paginated_messages(graph_manager):
    messages = [HumanMessage(content=f"Message {i}") for i in range(10)]

    page1 = graph_manager._get_paginated_messages(messages, page=1, size=3)
    assert len(page1) == 3
    assert page1[0].content == "Message 0"
    assert page1[2].content == "Message 2"

    page2 = graph_manager._get_paginated_messages(messages, page=2, size=3)
    assert len(page2) == 3
    assert page2[0].content == "Message 3"
    assert page2[2].content == "Message 5"

    page4 = graph_manager._get_paginated_messages(messages, page=4, size=3)
    assert len(page4) == 1
    assert page4[0].content == "Message 9"


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages(mocker, graph_manager):
    thread_id = "test-thread-id"

    mock_messages = [
        HumanMessage(content="Test question"),
        AIMessage(content="Test response"),
    ]

    mock_checkpoint = mocker.Mock(spec=CheckpointTuple)
    mock_checkpoint.checkpoint = {"channel_values": {"messages": mock_messages}}

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        graph_manager, "_get_latest_checkpoint_messages", side_effect=mock_get_messages
    )

    result = await graph_manager._get_latest_checkpoint_messages(thread_id)

    assert result == mock_messages


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages_no_checkpoint(graph_manager, mocker):
    thread_id = "test-thread-id"

    async def mock_get_no_messages(_thread_id):
        return None

    mocker.patch.object(
        graph_manager,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await graph_manager._get_latest_checkpoint_messages(thread_id)

    assert result is None


@pytest.mark.anyio
async def test_get_historical_messages(graph_manager, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 2

    mock_messages = [
        HumanMessage(id="1", content="Hello"),
        AIMessage(id="2", content="Hi there!"),
        ToolMessage(
            id="3", content="Tool result", name="test_tool", tool_call_id="call_123"
        ),
    ]

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        graph_manager, "_get_latest_checkpoint_messages", side_effect=mock_get_messages
    )

    result = await graph_manager.get_historical_messages(thread_id, page, size)

    assert result is not None
    assert result.pagination.thread_id == thread_id
    assert result.pagination.current_page == page
    assert result.pagination.page_size == size
    assert result.pagination.total_messages == 3
    assert result.pagination.total_pages == 2

    assert len(result.messages) == 2
    assert result.messages[0].role == "user"
    assert result.messages[0].content == "Hello"

    assert result.messages[1].role == "assistant"
    assert result.messages[1].content == "Hi there!"

    graph_manager._get_latest_checkpoint_messages.assert_called_once_with(thread_id)


@pytest.mark.anyio
async def test_get_historical_messages_no_messages(graph_manager, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 5

    async def mock_get_no_messages(_thread_id):
        return None

    mocker.patch.object(
        graph_manager,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await graph_manager.get_historical_messages(thread_id, page, size)

    assert result is None
    graph_manager._get_latest_checkpoint_messages.assert_called_once_with(thread_id)
