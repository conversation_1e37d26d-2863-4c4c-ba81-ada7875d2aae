from typing import Any
from uuid import UUID

from app.core.database import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.workspace.integrations.user_integrations import create_user_integrations


# todo: remove it since workspace is using async db sessions and app.integrations methods are async now
class AsyncCRMProxy:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def _execute_crm_method(self, method_name: str, *args, **kwargs):
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            crm = await user_integrations.crm()
            if not crm:
                raise RuntimeError(f"No CRM integration for user {self.user_id}")

            method = getattr(crm, method_name)
            return await method(*args, **kwargs)

    async def get_current_user_territory(self) -> list[dict[str, Any]]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )
            return await user_integrations.get_crm_accounts()

    def __getattr__(self, name: str):
        async def async_method(*args, **kwargs):
            return await self._execute_crm_method(name, *args, **kwargs)

        return async_method
