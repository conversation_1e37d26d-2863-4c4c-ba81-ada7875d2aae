import uuid
from typing import Any

from langchain_core.messages import AIMessage, HumanMessage
from langfuse import Langfuse, get_client
from langfuse._client.datasets import DatasetItemClient
from langfuse._client.span import (
    LangfuseSpan,
)
from langfuse.langchain import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.agentic.graph.graph import GraphFactory
from app.agentic.graph.graph_manager import GraphManager
from app.common.helpers.logger import get_logger
from app.core.config import config
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    create_user_integrations,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.organization import OrganizationService
from app.workspace.types import EnvironmentType

logger = get_logger()


class LangfuseEvaluationPipeline:
    def __init__(
        self,
        dataset_name: str,
        user_id: uuid.UUID,
        org_id: uuid.UUID,
        crm_account_id: str,
        env_type: EnvironmentType,
        experiment_name: str | None = "test_experiment",
    ):
        self.dataset_name = dataset_name
        self.org_id = org_id
        self.user_id = user_id
        self.crm_account_id = crm_account_id
        self.env_type = env_type
        self.experiment_name = experiment_name

        Langfuse(
            public_key=config.langfuse_public_key,
            secret_key=config.langfuse_secret_key,
            host=config.langfuse_host,
            environment="evaluation",
        )

        self.langfuse_client = get_client()
        self.langfuse_callback_handler = CallbackHandler()

        self.db_session = AsyncSessionLocal()

        org_repo = OrganizationRepository(self.db_session)
        env_repo = EnvironmentRepository(self.db_session)

        self.org_service = OrganizationService(
            db_session=self.db_session,
            org_repo=org_repo,
            env_repo=env_repo,
        )

        self.environment: OrgEnvironment | None = None

    async def _initialize(self) -> None:
        if self.environment is None:
            self.environment = await self._get_environment()

            if self.environment is None:
                raise RuntimeError("Failed to initialize environment")

    async def _get_environment(self) -> OrgEnvironment:
        environment = await self.org_service.get_env(
            org_id=self.org_id, env_type=self.env_type
        )
        if not environment:
            raise RuntimeError(
                f"No environment found for org {self.org_id} and type {self.env_type}"
            )
        return environment

    async def _setup_graph_manager(self) -> GraphManager:
        user_integrations = await create_user_integrations(
            user_id=self.user_id,
            environment_id=self.environment.id,
            db_session=self.db_session,
        )

        graph_factory = GraphFactory(self.user_id, user_integrations)
        graph_definition = await graph_factory.create_graph()
        compiled_graph = graph_definition.compile()

        return GraphManager(
            graph=compiled_graph, langfuse_handler=self.langfuse_callback_handler
        )

    async def _process_and_evaluate_item(
        self, item: DatasetItemClient, graph_manager: GraphManager
    ) -> dict[str, Any]:
        with item.run(
            run_name=self.experiment_name,
        ) as root_span:
            graph_input = {
                "messages": [HumanMessage(item.input)],
                "crm_account_id": self.crm_account_id,
                "thread_id": None,
                "resume": False,
                "org_id": self.org_id,
                "user_id": self.user_id,
            }

            response = await graph_manager.invoke_graph(graph_input)
            actual_sequence = self.extract_sequence(response)
            score_data = self.evaluate_sequence(actual_sequence, item.expected_output)

            self._log_score_to_langfuse(root_span, score_data, actual_sequence)

            root_span.score_trace(
                name=score_data["name"],
                value=score_data["score"],
                comment=score_data["comment"],
                data_type="NUMERIC",
            )
            root_span.update_trace(
                output={
                    "actual_sequence": actual_sequence,
                    "evaluation_score_data": score_data,
                }
            )

            return score_data

    def _log_score_to_langfuse(
        self,
        span: LangfuseSpan,
        score_data: dict[str, Any],
        actual_sequence: list[dict[str, Any]],
    ) -> None:
        span.score_trace(
            name=score_data["name"],
            value=score_data["score"],
            comment=score_data["comment"],
            data_type="NUMERIC",
        )
        span.update_trace(
            output={
                "actual_sequence": actual_sequence,
                "evaluation_score_data": score_data,
            }
        )

    async def run_evaluation(self) -> dict[str, Any]:
        logger.info(f"Starting Langfuse evaluation for dataset {self.dataset_name}")
        graph_manager = await self._setup_graph_manager()
        dataset = self.langfuse_client.get_dataset(self.dataset_name)

        for item in dataset.items:
            try:
                logger.info(f"Evaluating item {item.id} with input: {item.input}")
                score_data = await self._process_and_evaluate_item(item, graph_manager)

                logger.info(
                    f"Item {item.id} -> Score: {score_data['score']}, "
                    f"Comment: {score_data['comment']}"
                )
            except Exception:
                logger.exception(
                    f"Error evaluating item {item.id} for dataset {self.dataset_name}"
                )

        logger.info(f"Evaluation finished for dataset {self.dataset_name}.")

    def extract_sequence(self, response: dict[str, Any]) -> list[dict[str, Any]]:
        messages = response.get("messages", [])
        tool_call_sequence = []
        for message in messages:
            if isinstance(message, AIMessage) and message.tool_calls:
                for tool_call in message.tool_calls:
                    tool_call_sequence.append(
                        {
                            "tool_name": tool_call.get("name"),
                            "tool_args": tool_call.get("args"),
                        }
                    )
        return tool_call_sequence

    def evaluate_sequence(
        self,
        actual_sequence: list[dict[str, Any]],
        expected_sequence: list[dict[str, Any]],
    ) -> dict[str, Any]:
        if not isinstance(expected_sequence, list):
            comment = f"FAIL: Expected output is not a list, but type {type(expected_sequence)}."
            return {"name": "tool_sequence_match", "score": 0, "comment": comment}

        if len(actual_sequence) != len(expected_sequence):
            comment = (
                f"FAIL: Sequence length mismatch. "
                f"Expected {len(expected_sequence)}, got {len(actual_sequence)}."
            )
            return {"name": "tool_sequence_match", "score": 0, "comment": comment}

        correct_steps = 0
        for i, expected_call in enumerate(expected_sequence):
            actual_call = actual_sequence[i]
            if actual_call["tool_name"] != expected_call["tool_name"]:
                comment = (
                    f"FAIL at step {i + 1}: Incorrect tool. "
                    f"Expected '{expected_call['tool_name']}', "
                    f"got '{actual_call['tool_name']}'."
                )
                return {"name": "tool_sequence_match", "score": 0, "comment": comment}
            correct_steps += 1

        score = correct_steps / len(expected_sequence) if expected_sequence else 1.0
        comment = (
            "OK"
            if score == 1.0
            else f"Partially correct: {correct_steps}/{len(expected_sequence)} steps matched."
        )
        return {"name": "tool_sequence_match", "score": score, "comment": comment}

    async def _close(self):
        if self.db_session:
            await self.db_session.close()

    async def __aenter__(self):
        await self._initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._close()
        self.langfuse_client.shutdown()
