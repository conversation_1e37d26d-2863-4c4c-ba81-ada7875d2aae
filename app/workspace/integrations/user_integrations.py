from typing import Any, cast
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.protocols import CRMResource, MessagingResource
from app.workspace.integrations.credentials_resolver import UserCredentialsResolver
from app.workspace.models import IntegrationConfig, OAuthToken
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import IntegrationType

IntegrationProvider = CRMResource | MessagingResource

logger = get_logger()


class UserIntegrations:
    """
    Manages integration access for a specific user in an organization.

    Provides a clean interface to various integration providers (CRM, Messaging, etc.) by
    automatically selecting the appropriate configuration and credentials based on
    the user and organization context.
    """

    def __init__(
        self,
        user_id: UUID,
        environment: OrgEnvironment,
        integration_cfg_service: IntegrationConfigService,
        salesforce_connection_service: SalesforceConnectionService,
        db_session: AsyncSession,
    ):
        self.integration_cfg_service = integration_cfg_service
        self.salesforce_connection_service = salesforce_connection_service
        self.db_session = db_session
        self.user_id = user_id
        self.environment = environment
        self.org_id = environment.organization_id
        self.env_type = environment.type

        self._factory = self._create_factory()

        self._resources: dict[IntegrationType, Any] = {}
        self._configs: dict[IntegrationType, IntegrationConfig | None] = {}
        self._tokens: dict[IntegrationType, OAuthToken | None] = {}

    async def crm(self) -> CRMResource | None:
        resource = await self._get_resource(IntegrationType.CRM)
        return cast("CRMResource", resource) if resource else None

    async def get_crm_accounts(self) -> list[dict[str, Any]]:
        crm = await self.crm()
        if not crm:
            return []

        crm_user_id = await self.crm_user_id()
        if not crm_user_id:
            return []

        return await crm.list_account_access(crm_user_id)

    async def sync_crm_accounts(self) -> None:
        crm = await self.crm()
        crm_user_id = await self.crm_user_id()
        if crm and crm_user_id:
            await crm.bulk_sync_account_access(crm_user_ids=[crm_user_id])

    async def crm_user_id(self) -> str | None:
        token = await self._get_token(IntegrationType.CRM)
        return token.external_user_id if token else None

    async def crm_org_id(self) -> str | None:
        token = await self._get_token(IntegrationType.CRM)
        return token.external_org_id if token else None

    def _create_factory(self) -> IntegrationFactory:
        credentials_resolver = UserCredentialsResolver(
            environment=self.environment,
            user_id=self.user_id,
            integration_config_service=self.integration_cfg_service,
            salesforce_connection_service=self.salesforce_connection_service,
        )

        return create_factory(
            tenant_id=self.environment.id,
            db_session=self.db_session,
            credentials_resolver=credentials_resolver,
        )

    async def _get_resource(
        self, integration_type: IntegrationType
    ) -> IntegrationProvider | None:
        if integration_type in self._resources:
            return self._resources[integration_type]

        try:
            config = await self._get_config(integration_type)
            if not config:
                logger.warning(
                    f"No active {integration_type.value} integration found for organization {self.org_id}"
                )
                return None

            resource: Any
            if integration_type == IntegrationType.CRM:
                token = await self._get_token(IntegrationType.CRM)
                if not token:
                    logger.warning(
                        f"No CRM OAuth token found for user {self.user_id}, integration {config.id}"
                    )
                    return None

                resource = self._factory.crm(config.source)
            else:
                raise ValueError(
                    f"Provider type {integration_type} not yet implemented"
                )

            if resource is not None:
                self._resources[integration_type] = resource

            return resource

        except Exception:
            logger.exception(f"Error initializing {integration_type.value} provider")
            return None

    async def _get_config(
        self, integration_type: IntegrationType
    ) -> IntegrationConfig | None:
        if integration_type in self._configs:
            return self._configs[integration_type]

        if integration_type == IntegrationType.CRM:
            config = await self.integration_cfg_service.get_crm_config(
                environment=self.environment
            )
        else:
            logger.warning(f"Config type {integration_type} not yet implemented")
            config = None

        if config is not None:
            self._configs[integration_type] = config

        return config

    async def _get_token(self, integration_type: IntegrationType) -> OAuthToken | None:
        if integration_type in self._tokens:
            return self._tokens[integration_type]

        config = await self._get_config(integration_type)
        if not config:
            return None

        token = await self.integration_cfg_service.get_oauth_token_for_user(
            integration_config_id=config.id, user_id=self.user_id
        )

        if token is not None:
            self._tokens[integration_type] = token

        return token


async def create_user_integrations(
    user_id: UUID,
    environment_id: UUID,
    db_session: AsyncSession,
) -> UserIntegrations:
    env_repo = EnvironmentRepository(db_session)
    environment = await env_repo.get_by_id(environment_id)

    integration_cfg_repo = IntegrationConfigRepository(db_session)
    oauth_token_repo = OAuthTokenRepository(db_session)

    integration_cfg_service = IntegrationConfigService(
        integration_cfg_repo=integration_cfg_repo,
        oauth_token_repo=oauth_token_repo,
    )
    salesforce_connection_service = SalesforceConnectionService(
        db_session=db_session,
        oauth_token_repo=oauth_token_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.salesforce.auth_url),
        token_url=str(config.salesforce.token_url),
        redirect_uri=str(config.salesforce.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )

    return UserIntegrations(
        user_id=user_id,
        environment=OrgEnvironment.model_validate(environment),
        integration_cfg_service=integration_cfg_service,
        salesforce_connection_service=salesforce_connection_service,
        db_session=db_session,
    )
