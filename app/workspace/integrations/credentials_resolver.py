import uuid
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.types import IntegrationSource
from app.workspace.models import OAuthToken
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService

logger = get_logger()


class WorkspaceCredentials(ICredentials):
    def __init__(self, secrets: dict[str, Any]):
        self._secrets = secrets

    @property
    def secrets(self) -> dict[str, Any]:
        return self._secrets

    async def refresh_token(self) -> "ICredentials":
        return self


class WorkspaceSalesforceCredentials(ICredentials):
    def __init__(
        self,
        oauth_token: OAuthToken,
        connection_service: SalesforceConnectionService,
        environment: OrgEnvironment,
    ):
        self._oauth_token = oauth_token
        self._connection_service = connection_service
        self._environment = environment

    @property
    def secrets(self) -> dict[str, Any]:
        return {
            "access_token": self._oauth_token.access_token,
            "instance_url": self._oauth_token.instance_url,
        }

    async def refresh_token(self) -> "WorkspaceSalesforceCredentials":
        try:
            token_response = await self._connection_service.refresh_access_token(
                oauth_token_id=self._oauth_token.id,
                environment=self._environment,
            )
            self._oauth_token.access_token = token_response.access_token
            if token_response.instance_url:
                self._oauth_token.instance_url = token_response.instance_url
            logger.info(
                f"Successfully refreshed token for user {self._oauth_token.user_id}"
            )
        except Exception as e:
            logger.error(
                f"Failed to refresh token for user {self._oauth_token.user_id}: {e}"
            )

        return self


class SimpleCredentialsResolver(ICredentialsResolver):
    def __init__(self, credentials: ICredentials):
        self.credentials = credentials

    async def get_credentials(self, *_args, **_kwargs) -> ICredentials:
        return self.credentials


class OrganizationCredentialsResolver(ICredentialsResolver):
    """
    Resolver that uses organization-level credentials.
    """

    def __init__(
        self,
        environment: OrgEnvironment,
        integration_config_service: IntegrationConfigService,
    ):
        self.environment = environment
        self.integration_config_service = integration_config_service

    async def get_credentials(self, source: IntegrationSource) -> WorkspaceCredentials:
        integration_config = (
            await self.integration_config_service.get_integration_config(
                environment=self.environment, source=source
            )
        )

        if not integration_config:
            logger.warning(
                f"No credentials found for org {self.environment.organization_id}, source {source}"
            )
            return WorkspaceCredentials({})

        return WorkspaceCredentials(integration_config.credentials)


class UserCredentialsResolver(ICredentialsResolver):
    """
    Resolver that uses user-specific credentials when available.
    For OAuth-supporting sources: returns empty credentials if no user token exists.
    For other sources: falls back to organization credentials automatically.
    """

    def __init__(
        self,
        environment: OrgEnvironment,
        user_id: uuid.UUID,
        integration_config_service: IntegrationConfigService,
        salesforce_connection_service: SalesforceConnectionService,
    ):
        self.environment = environment
        self.user_id = user_id
        self.integration_config_service = integration_config_service
        self.salesforce_connection_service = salesforce_connection_service

    async def get_credentials(
        self, source: IntegrationSource
    ) -> WorkspaceCredentials | WorkspaceSalesforceCredentials:
        integration_config = (
            await self.integration_config_service.get_integration_config(
                environment=self.environment, source=source
            )
        )

        if not integration_config:
            logger.warning(
                f"No integration config found for org {self.environment.organization_id}, source {source}"
            )
            return WorkspaceCredentials({})

        if source is IntegrationSource.SALESFORCE:
            oauth_token = (
                await self.integration_config_service.get_oauth_token_for_user(
                    integration_config_id=integration_config.id,
                    user_id=self.user_id,
                )
            )
            if oauth_token:
                return WorkspaceSalesforceCredentials(
                    oauth_token=oauth_token,
                    environment=self.environment,
                    connection_service=self.salesforce_connection_service,
                )

            logger.warning(
                f"No OAuth token found for user {self.user_id}, source {source}"
            )
            return WorkspaceCredentials({})

        return WorkspaceCredentials(integration_config.credentials)
