import uuid
from typing import Any

from app.auth.repository import UserRepository
from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.core.database import AsyncSessionLocal
from app.integrations.factory import create_factory
from app.workspace.integrations.credentials_resolver import (
    ICredentialsResolver,
    SimpleCredentialsResolver,
    WorkspaceSalesforceCredentials,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.organization import OrganizationService
from app.workspace.services.organization_team import OrganizationTeamService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType

logger = get_logger()


class CRMWorkflow:
    def __init__(
        self,
        org_id: uuid.UUID,
        env_type: EnvironmentType = EnvironmentType.PROD,
    ):
        self.org_id = org_id
        self.env_type = env_type

        self.db_session = AsyncSessionLocal()

        integration_cfg_repo = IntegrationConfigRepository(self.db_session)
        oauth_token_repo = OAuthTokenRepository(self.db_session)
        org_repo = OrganizationRepository(self.db_session)
        org_member_repo = OrganizationMemberRepository(self.db_session)
        env_repo = EnvironmentRepository(self.db_session)
        user_repo = UserRepository(self.db_session)

        self.org_service = OrganizationService(
            db_session=self.db_session,
            org_repo=org_repo,
            env_repo=env_repo,
        )
        self.integration_cfg_service = IntegrationConfigService(
            integration_cfg_repo=integration_cfg_repo,
            oauth_token_repo=oauth_token_repo,
        )
        self.org_team_service = OrganizationTeamService(
            db_session=self.db_session,
            org_member_repo=org_member_repo,
            org_repo=org_repo,
            user_repo=user_repo,
        )
        self.salesforce_connection_service = SalesforceConnectionService(
            db_session=self.db_session,
            oauth_token_repo=oauth_token_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=str(config.salesforce.auth_url),
            token_url=str(config.salesforce.token_url),
            redirect_uri=str(config.salesforce.redirect_uri),
            flow_type=OAuthFlowType.PKCE,
        )

        self.environment: OrgEnvironment | None = None

    async def initialize(self) -> None:
        if self.environment is None:
            self.environment = await self._get_environment()

    async def _get_environment(self) -> OrgEnvironment:
        environment = await self.org_service.get_env(
            org_id=self.org_id, env_type=self.env_type
        )
        if not environment:
            raise RuntimeError(
                f"No environment found for org {self.org_id} and type {self.env_type}"
            )
        return environment

    async def sync_account_access(
        self,
        interval: int = 300,
        daemon: bool = False,
    ) -> dict[str, Any]:
        if self.environment is None:
            await self.initialize()

        if self.environment is None:
            raise RuntimeError("Failed to initialize environment")

        failed_users: list[str] = []

        result: dict[str, Any] = {
            "status": "success",
            "user_count": 0,
            "details": None,
            "failed_users": failed_users,
        }

        try:
            logger.info(
                f"Starting Salesforce account access sync for organization {self.org_id}"
            )

            crm_config = await self.integration_cfg_service.get_crm_config(
                environment=self.environment
            )

            if not crm_config:
                msg = f"No active CRM integration found for organization {self.org_id}"
                logger.error(msg)
                result["status"] = "error"
                result["error"] = msg
                return result

            db_user_ids = await self.org_team_service.get_team_user_ids(self.org_id)

            if not db_user_ids:
                msg = f"No users found for organization {self.org_id}"
                logger.error(msg)
                result["status"] = "error"
                result["error"] = msg
                return result

            logger.info(f"Found {len(db_user_ids)} users in organization {self.org_id}")
            result["user_count"] = len(db_user_ids)

            (
                crm_user_ids,
                user_credentials_map,
                failed_users_list,
            ) = await self._get_crm_user_ids_and_credentials(
                list(db_user_ids), crm_config.id
            )

            failed_users.extend(failed_users_list)

            if not crm_user_ids:
                msg = "No valid CRM user IDs found for any users in the organization"
                logger.error(msg)
                result["status"] = "error"
                result["error"] = msg
                return result

            def get_user_credentials_resolver(crm_user_id: str) -> ICredentialsResolver:
                return user_credentials_map[crm_user_id]

            integration_factory = create_factory(
                tenant_id=self.environment.id,
                db_session_factory=AsyncSessionLocal,  # repositories are not async in app.integrations yet
            )

            crm = integration_factory.crm(source=crm_config.source)

            sync_result = await crm.bulk_sync_account_access(
                crm_user_ids=crm_user_ids,
                interval_seconds=interval,
                daemon_mode=daemon,
                get_credentials_resolver=get_user_credentials_resolver,
            )

            if daemon:
                logger.info("Salesforce account access sync daemon has stopped")
                result["daemon_mode"] = True
            else:
                logger.info("Salesforce account access sync completed successfully!")
                result["details"] = sync_result

            return result

        except Exception as e:
            logger.exception("Error during Salesforce account access sync")
            result["status"] = "error"
            result["error"] = str(e)
            return result

    async def _get_crm_user_ids_and_credentials(
        self, db_user_ids: list[uuid.UUID], integration_config_id: uuid.UUID
    ) -> tuple[list[str], dict[str, ICredentialsResolver], list[str]]:
        if self.environment is None:
            raise RuntimeError("Environment not initialized - call initialize() first")

        crm_user_ids: list[str] = []
        user_credentials_map: dict[str, ICredentialsResolver] = {}
        failed_users: list[str] = []

        for user_id in db_user_ids:
            token = await self.integration_cfg_service.get_oauth_token_for_user(
                integration_config_id=integration_config_id, user_id=user_id
            )

            if token and token.external_user_id:
                crm_user_ids.append(token.external_user_id)

                user_credentials_map[token.external_user_id] = (
                    SimpleCredentialsResolver(
                        WorkspaceSalesforceCredentials(
                            oauth_token=token,
                            environment=self.environment,
                            connection_service=self.salesforce_connection_service,
                        )
                    )
                )
            else:
                logger.warning(f"No CRM user ID found for internal user {user_id}")
                failed_users.append(str(user_id))

        return crm_user_ids, user_credentials_map, failed_users

    async def close(self):
        if self.db_session:
            await self.db_session.close()

    async def __aenter__(self):
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
