# ruff: noqa: S106

import uuid
from datetime import UTC, datetime, timedelta

import pytest

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig, OAuthToken
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.salesforce_connection import (
    SalesforceConnectionService,
    SalesforceTokenResponse,
)
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )


@pytest.fixture
def service_mocks(mocker):
    """Fixture pour tous les mocks nécessaires au service."""
    db_session_mock = mocker.Mock()
    db_session_mock.commit = mocker.AsyncMock()
    db_session_mock.refresh = mocker.AsyncMock()

    return {
        "db_session": db_session_mock,
        "oauth_token_repo": mocker.Mock(),
        "integration_cfg_repo": mocker.Mock(),
        "oauth_flow_manager": mocker.Mock(),
    }


@pytest.fixture
def salesforce_connection_service(service_mocks):
    service = SalesforceConnectionService(
        db_session=service_mocks["db_session"],
        oauth_token_repo=service_mocks["oauth_token_repo"],
        integration_cfg_repo=service_mocks["integration_cfg_repo"],
        auth_url="https://login.salesforce.com/services/oauth2/authorize",
        token_url="https://login.salesforce.com/services/oauth2/token",
        redirect_uri="https://app.example.com/oauth/callback",
        flow_type=OAuthFlowType.STANDARD,
    )
    service.oauth_flow_manager = service_mocks["oauth_flow_manager"]

    return service


@pytest.fixture
def test_data(mock_environment):
    user_id = uuid.uuid4()
    integration_config_id = uuid.uuid4()
    oauth_token_id = uuid.uuid4()

    integration_config = IntegrationConfig()
    integration_config.id = integration_config_id
    integration_config.organization_id = mock_environment.organization_id
    integration_config.source = IntegrationSource.SALESFORCE
    integration_config.credentials = {
        "client_id": "fake_client_id",
        "client_secret": "fake_client_secret",
    }

    oauth_token = OAuthToken()
    oauth_token.id = oauth_token_id
    oauth_token.user_id = user_id
    oauth_token.integration_config_id = integration_config_id
    oauth_token.external_user_id = "SF_USER_123"
    oauth_token.external_org_id = "SF_ORG_456"
    oauth_token.access_token = "fake_access_token"
    oauth_token.refresh_token = "fake_refresh_token"
    oauth_token.instance_url = "https://example.my.salesforce.com"
    oauth_token.scope = "refresh_token full"
    oauth_token.token_type = "Bearer"
    oauth_token.expires_at = datetime.now(UTC) + timedelta(hours=1)
    oauth_token.last_refreshed_at = datetime.now(UTC)

    return {
        "user_id": user_id,
        "environment": mock_environment,
        "integration_config_id": integration_config_id,
        "oauth_token_id": oauth_token_id,
        "integration_config": integration_config,
        "oauth_token": oauth_token,
    }


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = service_mocks[
        "integration_cfg_repo"
    ].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "oauth_flow_manager"
    ].generate_authorization_uri.return_value = "https://example.com/auth"

    result = await salesforce_connection_service.generate_oauth_authorization_uri(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
    )

    assert result == "https://example.com/auth"
    service_mocks["integration_cfg_repo"].get_by_org_and_source.assert_called_once_with(
        test_data["environment"].organization_id,
        IntegrationSource.SALESFORCE,
        test_data["environment"].type,
    )
    service_mocks["oauth_flow_manager"].generate_authorization_uri.assert_called_once()


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_no_config(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationConfigError):
        await salesforce_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_process_oauth_callback_new_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks["oauth_token_repo"].get_by_user_and_integration = mocker.AsyncMock(
        return_value=None
    )

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    created_token = OAuthToken()
    created_token.external_user_id = "SF_USER_123"
    created_token.external_org_id = "SF_ORG_456"
    created_token.access_token = "new_access_token"
    created_token.refresh_token = "new_refresh_token"
    created_token.instance_url = "https://example.my.salesforce.com"
    created_token.scope = "refresh_token full"
    created_token.token_type = "Bearer"
    created_token.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["oauth_token_repo"].create = mocker.AsyncMock(
        return_value=created_token
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "new_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["oauth_token_repo"].create.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_update_existing_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks["oauth_token_repo"].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["oauth_token"]
    )

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "access_token": "updated_access_token",
        "refresh_token": "updated_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    updated_token = OAuthToken()
    updated_token.external_user_id = "SF_USER_123"
    updated_token.external_org_id = "SF_ORG_456"
    updated_token.access_token = "updated_access_token"
    updated_token.refresh_token = "updated_refresh_token"
    updated_token.instance_url = "https://example.my.salesforce.com"
    updated_token.scope = "refresh_token full"
    updated_token.token_type = "Bearer"
    updated_token.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["oauth_token_repo"].update = mocker.AsyncMock(
        return_value=updated_token
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "updated_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["oauth_token_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_process_oauth_callback_no_config(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationConfigError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_refresh_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["oauth_token_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["oauth_token"]
    )
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )

    token_data = {
        "access_token": "refreshed_access_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].refresh_access_token = mocker.AsyncMock(
        return_value=token_data
    )

    refreshed_token = OAuthToken()
    refreshed_token.external_user_id = "SF_USER_123"
    refreshed_token.external_org_id = "SF_ORG_456"
    refreshed_token.access_token = "refreshed_access_token"
    refreshed_token.refresh_token = "fake_refresh_token"
    refreshed_token.instance_url = "https://example.my.salesforce.com"
    refreshed_token.scope = "refresh_token full"
    refreshed_token.token_type = "Bearer"
    refreshed_token.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["oauth_token_repo"].update = mocker.AsyncMock(
        return_value=refreshed_token
    )

    result = await salesforce_connection_service.refresh_access_token(
        oauth_token_id=test_data["oauth_token_id"],
        environment=test_data["environment"],
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "refreshed_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["oauth_token_repo"].get_by_id.assert_called_once_with(
        test_data["oauth_token_id"]
    )
    service_mocks["oauth_token_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(refreshed_token)


@pytest.mark.anyio
async def test_refresh_access_token_not_found(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["oauth_token_repo"].get_by_id = mocker.AsyncMock(return_value=None)

    with pytest.raises(IntegrationTokenNotFoundError):
        await salesforce_connection_service.refresh_access_token(
            oauth_token_id=test_data["oauth_token_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_refresh_access_token_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["oauth_token_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["oauth_token"]
    )

    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.refresh_access_token(
            oauth_token_id=test_data["oauth_token_id"],
            environment=test_data["environment"],
        )
