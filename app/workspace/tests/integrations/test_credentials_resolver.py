import uuid
from datetime import datetime

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.integrations.credentials_resolver import (
    OrganizationCredentialsResolver,
    SimpleCredentialsResolver,
    UserCredentialsResolver,
    WorkspaceCredentials,
    WorkspaceSalesforceCredentials,
)
from app.workspace.models import OAuthToken
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def create_mock_oauth_token() -> OAuthToken:
    token = OAuthToken()
    token.id = uuid.uuid4()
    token.access_token = "mock_access_token"
    token.instance_url = "https://test.salesforce.com"
    token.user_id = uuid.uuid4()
    return token


def create_mock_integration_config() -> object:
    mock_config = type("MockIntegrationConfig", (), {})()
    mock_config.id = uuid.uuid4()
    mock_config.credentials = {"api_key": "test_key", "api_secret": "test_secret"}
    return mock_config


@pytest.mark.anyio
async def test_workspace_credentials_secrets():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)

    assert credentials.secrets == secrets
    refreshed = await credentials.refresh_token()
    assert refreshed is credentials


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_secrets(mocker, mock_environment):
    token = create_mock_oauth_token()
    connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_refresh_success(
    mocker, mock_environment
):
    token = create_mock_oauth_token()
    connection_service = mocker.AsyncMock(spec=SalesforceConnectionService)

    response = mocker.MagicMock()
    response.access_token = "refreshed_access_token"
    response.instance_url = "https://refreshed.salesforce.com"

    connection_service.refresh_access_token.return_value = response

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    refreshed = await credentials.refresh_token()

    assert refreshed is credentials
    assert refreshed.secrets == {
        "access_token": "refreshed_access_token",
        "instance_url": "https://refreshed.salesforce.com",
    }


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_refresh_failure(
    mocker, mock_environment
):
    token = create_mock_oauth_token()
    connection_service = mocker.AsyncMock(spec=SalesforceConnectionService)

    connection_service.refresh_access_token.side_effect = Exception("Failed to refresh")

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    original_access_token = token.access_token
    original_instance_url = token.instance_url

    refreshed = await credentials.refresh_token()

    assert refreshed is credentials

    assert refreshed.secrets == {
        "access_token": original_access_token,
        "instance_url": original_instance_url,
    }


@pytest.mark.anyio
async def test_simple_credentials_resolver():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)
    resolver = SimpleCredentialsResolver(credentials)

    result = await resolver.get_credentials(source=IntegrationSource.SALESFORCE)
    assert result is credentials

    result = await resolver.get_credentials(IntegrationSource.SLACK)
    assert result is credentials


@pytest.mark.anyio
async def test_organization_resolver_get_credentials(mocker, mock_environment):
    mock_service = mocker.AsyncMock()
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials


@pytest.mark.anyio
async def test_organization_resolver_no_config_found(mocker, mock_environment):
    mock_service = mocker.AsyncMock()

    mock_service.get_integration_config.return_value = None

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


@pytest.mark.anyio
async def test_user_resolver_with_salesforce_credentials(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_config_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()
    mock_token = create_mock_oauth_token()

    mock_config_service.get_integration_config.return_value = mock_config
    mock_config_service.get_oauth_token_for_user.return_value = mock_token

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_config_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceSalesforceCredentials)
    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


@pytest.mark.anyio
async def test_user_resolver_no_oauth_token(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config
    mock_service.get_oauth_token_for_user.return_value = None

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


@pytest.mark.anyio
async def test_user_resolver_non_oauth_source(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SLACK)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials
