import os
from typing import Literal

from pydantic import AnyUrl, HttpUrl, PostgresDsn, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from app.core.env import Environment


class BaseConfig(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class SalesforceConfig(BaseConfig):
    auth_url: HttpUrl | None = None
    token_url: HttpUrl | None = None
    redirect_uri: AnyUrl | None = None

    model_config = SettingsConfigDict(env_prefix="SALESFORCE_")


class MailerConfig(BaseConfig):
    email_client: Literal["customerio", "local"] = "local"
    from_email: str = "<EMAIL>"
    customerio_api_key: str = ""
    customerio_region: str = "us"

    model_config = SettingsConfigDict(env_prefix="MAILER_")


class DatabaseConfig(BaseConfig):
    database_url: PostgresDsn = PostgresDsn("postgresql://localhost/pearl_backend")
    async_database_url: PostgresDsn = PostgresDsn(
        "postgresql+asyncpg://localhost/pearl_backend"
    )
    test_database_url: PostgresDsn = PostgresDsn(
        "postgresql://localhost/pearl_backend_test"
    )
    test_async_database_url: PostgresDsn = PostgresDsn(
        "postgresql+asyncpg://localhost/pearl_backend_test"
    )

    @field_validator("database_url", mode="before")
    @classmethod
    def fix_postgres_scheme(cls, v: str) -> str:
        if isinstance(v, str) and v.startswith("postgres://"):
            # Heroku-style URL isn't supported by SQLAlchemy >= 1.4
            return v.replace("postgres://", "postgresql://", 1)
        return v

    @model_validator(mode="after")
    def validate_database_urls(self):
        db_url = self.database_url
        test_db_url = self.test_database_url
        if db_url == test_db_url:
            raise ValueError("database_url and test_database_url must be different")
        return self


class AppConfig(BaseConfig):
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    secret_key: str = ""
    project_title: str = "Pearl API"
    log_level: str = ""
    sqlalchemy_log_level: str = ""
    database: DatabaseConfig = DatabaseConfig()
    frontend_url: str = "http://localhost:3000"
    openai_api_key: str = ""
    gemini_api_key: str = ""
    linkup_api_key: str = ""
    langfuse_public_key: str = ""
    langfuse_secret_key: str = ""
    langfuse_host: str = "https://cloud.langfuse.com"
    salesforce: SalesforceConfig = SalesforceConfig()
    mailer: MailerConfig = MailerConfig()

    @model_validator(mode="after")
    def set_log_level_if_empty(self):
        if self.log_level == "":
            self.log_level = "DEBUG" if self.debug else "INFO"
        if self.sqlalchemy_log_level == "":
            self.sqlalchemy_log_level = "DEBUG" if self.debug else "INFO"
        return self


class DevConfig(AppConfig):
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True


class StagingConfig(AppConfig):
    environment: Environment = Environment.STAGING


class ProdConfig(AppConfig):
    environment: Environment = Environment.PRODUCTION


class TestConfig(AppConfig):
    environment: Environment = Environment.TEST
    log_level: str = "WARNING"


env = os.getenv("APP_ENV", "development")
config: AppConfig
match env:
    case Environment.PRODUCTION:
        config = ProdConfig()
    case Environment.STAGING:
        config = StagingConfig()
    case Environment.TEST:
        config = TestConfig()
    case _:
        config = DevConfig()
