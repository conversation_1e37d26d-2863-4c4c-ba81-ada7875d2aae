from io import Bytes<PERSON>
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.integrations.backends.file.backend import FileBackend
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials():
    """Mock credentials."""
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {"test_key": "test_value"}
    return credentials


@pytest.fixture
def mock_credentials_resolver(mock_credentials):
    """Mock credentials resolver."""
    resolver = Mock()
    resolver.get_credentials.return_value = mock_credentials
    return resolver


@pytest.fixture
def mock_context(mock_credentials_resolver):
    """Mock integration context."""
    context = Mock(spec=IntegrationContext)
    context.credentials_resolver = mock_credentials_resolver
    return context


@pytest.fixture
def mock_adapter_class():
    """Mock adapter class."""
    adapter_class = Mock(spec=type[BaseFileAdapter])
    mock_adapter_instance = AsyncMock(spec=BaseFileAdapter)
    adapter_class.return_value = mock_adapter_instance
    return adapter_class


@pytest.fixture
def file_backend(mock_context, mock_adapter_class):
    """Create FileBackend instance."""
    return FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )


def test_file_backend_initialization(mock_context, mock_adapter_class):
    backend = FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )

    assert backend._context == mock_context
    assert backend._adapter_class == mock_adapter_class
    assert backend.source == IntegrationSource.GCS


@pytest.mark.anyio
async def test_upload_file(file_backend):
    file_content = b"test file content"
    file_obj = BytesIO(file_content)
    bucket_name = "test-bucket"
    file_name = "test-file.txt"

    await file_backend.upload_file(bucket_name, file_obj, file_name)

    adapter = file_backend.get_adapter()
    adapter.upload_file.assert_called_once_with(bucket_name, file_obj, file_name)


@pytest.mark.anyio
async def test_upload_file_different_types(file_backend):
    test_files = [
        (b"text content", "document.txt"),
        (b"\x89PNG\r\n\x1a\n", "image.png"),
        (b"PDF-1.4", "document.pdf"),
    ]

    adapter = file_backend.get_adapter()

    for content, filename in test_files:
        file_obj = BytesIO(content)
        await file_backend.upload_file("test-bucket", file_obj, filename)

    assert adapter.upload_file.call_count == len(test_files)
