import uuid

import pytest

from app.common.pipeline.base_stage import BaseStage
from app.integrations.backends.crm.bulk_access_sync_stage import AccountAccessSyncStage
from app.integrations.schemas import CRMAccountAccessSyncResult
from app.integrations.types import IntegrationSource


@pytest.mark.anyio
async def test_execute_once_success(mocker, db_session):
    db_session.add = mocker.Mock()
    db_session.commit = mocker.AsyncMock()
    db_session.query = mocker.Mock()

    mock_query = mocker.Mock()
    mock_query.all.return_value = []
    db_session.query.return_value = mock_query

    tenant_id = uuid.uuid4()
    synchronizer1 = mocker.Mock()
    synchronizer2 = mocker.Mock()

    result1 = CRMAccountAccessSyncResult(new_access_count=5, old_access_count=3)
    result2 = CRMAccountAccessSyncResult(new_access_count=3, old_access_count=2)

    async def async_return_result1(_user_id):
        return result1

    async def async_return_result2(_user_id):
        return result2

    synchronizer1.sync_user_access.side_effect = async_return_result1
    synchronizer2.sync_user_access.side_effect = async_return_result2

    user_synchronizers = [("user1", synchronizer1), ("user2", synchronizer2)]

    stage = AccountAccessSyncStage(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session=db_session,
        user_synchronizers=user_synchronizers,
        interval_seconds=300,
    )

    result = await stage.execute_once()

    assert result["status"] == "success"
    assert result["users_processed"] == 2

    user1_result = result["users"]["user1"]
    assert user1_result["status"] == "success"
    assert user1_result["new_access_count"] == 5
    assert user1_result["old_access_count"] == 3

    user2_result = result["users"]["user2"]
    assert user2_result["status"] == "success"
    assert user2_result["new_access_count"] == 3
    assert user2_result["old_access_count"] == 2

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["successful_runs"] == 1
    assert stage.metrics["users_processed"] == 2
    assert stage.metrics["errors_count"] == 0
    assert stage.metrics["total_access_records"] == 8

    synchronizer1.sync_user_access.assert_called_once_with("user1")
    synchronizer2.sync_user_access.assert_called_once_with("user2")

    # Verify db operations were called (2 users, 2 commits each = 4 total)
    assert db_session.add.call_count == 2
    assert db_session.commit.call_count == 4  # 2 commits per user (add + update run)


@pytest.mark.anyio
async def test_execute_once_partial(mocker, db_session):
    db_session.add = mocker.Mock()
    db_session.commit = mocker.AsyncMock()
    db_session.query = mocker.Mock()

    mock_query = mocker.Mock()
    mock_query.all.return_value = []
    db_session.query.return_value = mock_query

    tenant_id = uuid.uuid4()
    synchronizer1 = mocker.Mock()
    synchronizer2 = mocker.Mock()

    result1 = CRMAccountAccessSyncResult(new_access_count=5, old_access_count=3)

    async def async_return_result1(_user_id):
        return result1

    synchronizer1.sync_user_access.side_effect = async_return_result1
    synchronizer2.sync_user_access.side_effect = Exception(
        "Synchronization failed for user2"
    )

    user_synchronizers = [("user1", synchronizer1), ("user2", synchronizer2)]

    stage = AccountAccessSyncStage(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session=db_session,
        user_synchronizers=user_synchronizers,
        interval_seconds=300,
    )

    result = await stage.execute_once()

    assert result["status"] == "partial"
    assert result["users_processed"] == 1

    user1_result = result["users"]["user1"]
    assert user1_result["status"] == "success"
    assert user1_result["new_access_count"] == 5
    assert user1_result["old_access_count"] == 3

    user2_result = result["users"]["user2"]
    assert user2_result["status"] == "error"
    assert "Synchronization failed for user2" in user2_result["error"]

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["successful_runs"] == 0
    assert stage.metrics["users_processed"] == 1
    assert stage.metrics["errors_count"] == 1
    assert stage.metrics["total_access_records"] == 5

    synchronizer1.sync_user_access.assert_called_once_with("user1")
    synchronizer2.sync_user_access.assert_called_once_with("user2")

    # Verify db operations were called (2 users, 2 commits each = 4 total)
    assert db_session.add.call_count == 2
    assert db_session.commit.call_count == 4  # 2 commits per user (add + update run)


def test_get_status(mocker):
    tenant_id = uuid.UUID("********-1234-5678-1234-5678********")

    dummy_base_status = {"base": True}
    mocker.patch.object(BaseStage, "get_status", return_value=dummy_base_status)

    synchronizer1 = mocker.Mock()
    synchronizer2 = mocker.Mock()

    user_synchronizers = [("user1", synchronizer1), ("user2", synchronizer2)]

    session = mocker.Mock()

    stage = AccountAccessSyncStage(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        db_session=session,
        user_synchronizers=user_synchronizers,
        interval_seconds=300,
    )

    status = stage.get_status()

    assert status["base"] is True
    assert status["tenant_id"] == "********-1234-5678-1234-5678********"
    assert status["users_count"] == len(user_synchronizers)
    assert status["interval_seconds"] == 300
    assert "metrics" in status

    assert status["metrics"]["total_runs"] == 0
    assert status["metrics"]["successful_runs"] == 0
    assert status["metrics"]["users_processed"] == 0
    assert status["metrics"]["errors_count"] == 0
