import uuid

import pytest

from app.common.pipeline.runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.backends.messaging.channel_ingestor import (
    MessagingChannelIngestor,
)
from app.integrations.backends.messaging.ingest_runner import MessagingIngestRunner
from app.integrations.backends.messaging.ingest_stage import MessagingIngestStage
from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {"slack_token": "xoxb-test-token"}
    return mock_creds


@pytest.fixture
def mock_credentials_resolver(mocker, mock_credentials):
    mock_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_resolver.get_credentials = mocker.AsyncMock(return_value=mock_credentials)
    return mock_resolver


@pytest.fixture
def mock_context(mocker, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()

    def create_mock_session():
        mock_session = mocker.Mock()
        mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = mocker.AsyncMock(return_value=None)
        return mock_session

    mock_context.db_session_factory = create_mock_session
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def mock_adapter_class(mocker):
    mock_class = mocker.Mock()
    return mock_class


@pytest.fixture
def ingest_runner(mock_context, mock_adapter_class):
    return MessagingIngestRunner(
        context=mock_context,
        source=IntegrationSource.SLACK,
        adapter_class=mock_adapter_class,
    )


def test_init(mock_context, mock_adapter_class):
    runner = MessagingIngestRunner(
        context=mock_context,
        source=IntegrationSource.SLACK,
        adapter_class=mock_adapter_class,
    )

    assert runner.context == mock_context
    assert runner.source == IntegrationSource.SLACK
    assert runner.tenant_id == mock_context.tenant_id
    assert runner.db_session_factory == mock_context.db_session_factory
    assert runner.adapter_class == mock_adapter_class


@pytest.mark.anyio
async def test_get_adapter(ingest_runner, mock_credentials):
    adapter = await ingest_runner.get_adapter()

    ingest_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SLACK
    )

    ingest_runner.adapter_class.assert_called_once_with(credentials=mock_credentials)

    assert adapter == ingest_runner.adapter_class.return_value


@pytest.mark.anyio
async def test_run_normal_mode(mocker, ingest_runner):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.run = mocker.AsyncMock(
        return_value={"status": "success", "details": "test results"}
    )
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PipelineRunner",
        return_value=mock_pipeline,
    )

    mock_message_store_class = mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PostgresMessageStore"
    )

    mock_ingestor = mocker.Mock(spec=MessagingChannelIngestor)
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingChannelIngestor",
        return_value=mock_ingestor,
    )

    mock_stage = mocker.Mock(spec=MessagingIngestStage)
    stage_init_mock = mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingIngestStage",
        return_value=mock_stage,
    )

    channel_ids = ["channel1", "channel2"]
    result = await ingest_runner.run(
        channel_ids=channel_ids,
        interval_seconds=300,
        lookback_days=7,
        batch_size=100,
        daemon_mode=False,
    )

    ingest_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SLACK
    )

    ingest_runner.adapter_class.assert_called_once()
    call_args = ingest_runner.adapter_class.call_args
    assert "credentials" in call_args.kwargs

    mock_message_store_class.assert_called_once()
    store_args = mock_message_store_class.call_args.kwargs
    assert store_args["tenant_id"] == ingest_runner.tenant_id
    assert store_args["source"] == ingest_runner.source

    stage_init_mock.assert_called_once()
    stage_args = stage_init_mock.call_args.kwargs
    assert stage_args["tenant_id"] == ingest_runner.tenant_id
    assert stage_args["source"] == ingest_runner.source
    assert stage_args["ingestor"] == mock_ingestor
    assert stage_args["channel_ids"] == channel_ids
    assert stage_args["lookback_days"] == 7
    assert stage_args["interval_seconds"] == 300

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.run.call_count == 1
    assert result == {"status": "success", "details": "test results"}


@pytest.mark.anyio
async def test_run_daemon_mode(mocker, ingest_runner):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.start_daemon = mocker.AsyncMock()
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PipelineRunner",
        return_value=mock_pipeline,
    )

    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PostgresMessageStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingChannelIngestor"
    )
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingIngestStage"
    )

    result = await ingest_runner.run(
        channel_ids=["channel1"],
        daemon_mode=True,
    )

    ingest_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SLACK
    )

    ingest_runner.adapter_class.assert_called_once()
    call_args = ingest_runner.adapter_class.call_args
    assert "credentials" in call_args.kwargs

    assert mock_pipeline.add_stage.call_count == 1
    assert mock_pipeline.start_daemon.call_count == 1

    assert result == {"status": "daemon_stopped"}


@pytest.mark.anyio
async def test_run_custom_parameters(mocker, ingest_runner):
    mock_pipeline = mocker.Mock(spec=PipelineRunner)
    mock_pipeline.run = mocker.AsyncMock(return_value={})
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PipelineRunner",
        return_value=mock_pipeline,
    )

    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.PostgresMessageStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingChannelIngestor"
    )

    stage_init_mock = mocker.patch(
        "app.integrations.backends.messaging.ingest_runner.MessagingIngestStage"
    )

    custom_interval = 600
    custom_lookback = 14
    custom_batch_size = 200

    await ingest_runner.run(
        channel_ids=["channel1", "channel2"],
        interval_seconds=custom_interval,
        lookback_days=custom_lookback,
        batch_size=custom_batch_size,
    )

    ingest_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.SLACK
    )

    ingest_runner.adapter_class.assert_called_once()
    call_args = ingest_runner.adapter_class.call_args
    assert "credentials" in call_args.kwargs

    stage_init_mock.assert_called_once()
    stage_args = stage_init_mock.call_args.kwargs
    assert stage_args["interval_seconds"] == custom_interval
    assert stage_args["lookback_days"] == custom_lookback
    assert stage_args["source"] == ingest_runner.source


@pytest.mark.anyio
async def test_run_missing_credentials_resolver(mocker, mock_adapter_class):
    mock_context = mocker.Mock()
    mock_context.tenant_id = uuid.uuid4()

    def create_mock_session():
        mock_session = mocker.Mock()
        mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = mocker.AsyncMock(return_value=None)
        return mock_session

    mock_context.db_session_factory = create_mock_session
    mock_context.credentials_resolver = None

    runner = MessagingIngestRunner(
        context=mock_context,
        source=IntegrationSource.SLACK,
        adapter_class=mock_adapter_class,
    )

    with pytest.raises(RuntimeError):
        await runner.get_adapter()
