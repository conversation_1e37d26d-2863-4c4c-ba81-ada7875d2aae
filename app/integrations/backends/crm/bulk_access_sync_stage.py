import datetime
import uuid
from collections.abc import Sequence

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.pipeline.base_stage import BaseStage
from app.integrations.backends.crm.bulk_access_synchronizer import (
    AccountAccessSynchronizer,
)
from app.integrations.models import CRMAccountAccessSyncRun
from app.integrations.types import IntegrationSource

logger = get_logger()


class AccountAccessSyncStage(BaseStage):
    """
    Stage for synchronizing CRM account access data for users.

    This stage:
    - Coordinates synchronization for multiple Salesforce users
    - Tracks execution status and metrics
    - Handles periodic execution as part of a pipeline
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session: AsyncSession,
        user_synchronizers: Sequence[tuple[str, AccountAccessSynchronizer]],
        interval_seconds: int = 300,
        stage_id: str | None = None,
    ):
        """
        Initialize the Salesforce access synchronization stage.

        Args:
            tenant_id: Tenant ID
            source: Integration Source
            db_session: SQLAlchemy async session for tracking runs
            user_synchronizers: List of (user_id, synchronizer) pairs
            interval_seconds: Interval between execution cycles in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            stage_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.user_synchronizers = user_synchronizers
        self.tenant_id = tenant_id
        self.source = source
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "users_processed": 0,
            "errors_count": 0,
            "total_access_records": 0,
        }

    async def execute_once(self) -> dict:
        """
        Execute one cycle of Salesforce access synchronization for all configured users.

        Returns:
            Dictionary with execution results and metrics
        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "users_processed": 0,
            "users": {},
        }

        try:
            # Process each user with its dedicated synchronizer
            for crm_user_id, synchronizer in self.user_synchronizers:
                # Create run record to track execution
                run = CRMAccountAccessSyncRun(
                    tenant_id=self.tenant_id,
                    crm_user_id=crm_user_id,
                    status=CRMAccountAccessSyncRun.Status.IN_PROGRESS,
                    source=self.source,
                    run_start=datetime.datetime.now(datetime.UTC),
                )
                self.db_session.add(run)
                await self.db_session.commit()

                try:
                    self.logger.info(
                        f"Synchronizing access for Salesforce user {crm_user_id}"
                    )

                    # Use the user's synchronizer to handle this user
                    sync_result = await synchronizer.sync_user_access(crm_user_id)

                    # Update run status
                    run.status = CRMAccountAccessSyncRun.Status.SUCCESS
                    run.new_access_count = sync_result.new_access_count
                    run.old_access_count = sync_result.old_access_count
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    # Update statistics
                    results["users_processed"] += 1
                    self.metrics["users_processed"] += 1
                    self.metrics["total_access_records"] += sync_result.new_access_count

                    # Add user-specific results
                    results["users"][crm_user_id] = {
                        "status": "success",
                        "new_access_count": sync_result.new_access_count,
                        "old_access_count": sync_result.old_access_count,
                    }

                except Exception as e:
                    # Handle user-specific failures
                    run.status = CRMAccountAccessSyncRun.Status.FAILED
                    error_msg = str(e)
                    run.error_message = error_msg[:1024]
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    error_msg = (
                        f"Error synchronizing access for Salesforce user {crm_user_id}"
                    )
                    self.logger.exception(error_msg)
                    results["users"][crm_user_id] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # Determine overall status
            if any(u.get("status") == "error" for u in results["users"].values()):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            # Handle stage-level failures
            self.logger.exception(
                f"Salesforce access sync stage failed: {self.stage_id}"
            )
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        """
        Get detailed status information including metrics.

        Returns:
            Dictionary with current stage status and metrics
        """
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "users_count": len(self.user_synchronizers),
                "interval_seconds": self.interval_seconds,
                "metrics": self.metrics,
            }
        )
        return status
