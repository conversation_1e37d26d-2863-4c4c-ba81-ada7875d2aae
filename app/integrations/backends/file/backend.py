from typing import <PERSON><PERSON><PERSON>

from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


class FileBackend(BaseFileBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseFileAdapter],
        source: IntegrationSource,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )

    def get_adapter(self) -> BaseFileAdapter:
        if not hasattr(self, "_adapter"):
            credentials = self._context.credentials_resolver.get_credentials(
                self.source
            )
            self._adapter = self._adapter_class(credentials=credentials)
        return self._adapter

    async def upload_file(
        self,
        bucket_name: str,
        file_obj: BinaryIO,
        file_name: str,
    ) -> None:
        adapter = self.get_adapter()
        return await adapter.upload_file(bucket_name, file_obj, file_name)

    async def start_processing(self, bucket_name: str) -> None:
        pass
